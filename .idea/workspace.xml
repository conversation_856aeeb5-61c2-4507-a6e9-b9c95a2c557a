<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="64b99ec0-3378-4519-86aa-0bf3b1ee946b" name="Changes" comment="运营模块提交">
      <change afterPath="$PROJECT_DIR$/src/main/java/com/rutong/platform/app/controller/TestController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/java/com/rutong/platform/app/controller/TestsController.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/main/resources/application-pro.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/attach" beforeDir="true" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rutong/framework/security/auth/SmsAuthenticationProviderWeChat.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/rutong/framework/security/auth/SmsAuthenticationProviderWeChat.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/controller/EmployeeProductConfigurationController.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/controller/EmployeeProductConfigurationController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/entity/EmployeeProductConfiguration.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/entity/EmployeeProductConfiguration.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationHistoryService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationHistoryService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rutong/platform/client/util/ExportUtil.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/rutong/platform/common/service/BaseService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/rutong/platform/common/service/BaseService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target" beforeDir="true" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/pom.xml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/controller/EmployeeProductConfigurationController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/controller/DemandResponseController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/excel/EmployeeProductConfigurationExcel.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/app/controller/UnauthController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/app/controller/PdaController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/application.yml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/dto/EmployeeProductConfigurationDto.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/application-dev.yml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/app/controller/ClientController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/wms/controller/WmsExceptionController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/controller/ClauseInfoController.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/resources/application-pro.yml" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/ClauseInfoService.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/wms/service/WmsExceptionService.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/entity/ExpenseSettlementItem.java" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/entity/DemandResponse.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="useMavenConfig" value="true" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2izpAkKyHpOIVozIKgoh2Nm495j" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder0" value="0" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder1" value="1" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder2" value="2" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder3" value="3" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder4" value="4" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatOrder5" value="5" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth0" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth1" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth2" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth3" value="179" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth4" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_flatWidth5" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder0" value="0" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder1" value="1" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder2" value="2" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder3" value="3" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder4" value="4" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeOrder5" value="5" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth0" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth1" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth2" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth3" value="179" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth4" value="178" />
    <property name="FileHistory.org.jetbrains.idea.svn.history.SvnHistoryProvider_treeWidth5" value="178" />
    <property name="RequestMappingsPanelOrder0" value="0" />
    <property name="RequestMappingsPanelOrder1" value="1" />
    <property name="RequestMappingsPanelWidth0" value="75" />
    <property name="RequestMappingsPanelWidth1" value="75" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/src/main/resources" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.detected.package.tslint" value="true" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="node.js.selected.package.tslint" value="(autodetect)" />
    <property name="project.structure.last.edited" value="Problems" />
    <property name="project.structure.proportion" value="0.0" />
    <property name="project.structure.side.proportion" value="0.2" />
    <property name="settings.editor.selected.configurable" value="preferences.pluginManager" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/main/resources" />
      <recent name="$PROJECT_DIR$/src/main/java/com/rutong/framework/security/auth" />
      <recent name="$PROJECT_DIR$/src/main/resources/static/templates" />
      <recent name="$PROJECT_DIR$/src/main/java/com/rutong/platform/fms/controller" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.rutong.platform.device.controller" />
      <recent name="com.rutong.platform.device.service" />
      <recent name="com.rutong.platform.device.entity" />
      <recent name="com.rutong.platform.client.excel" />
      <recent name="com.rutong.platform.client.util" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.ClothingMgrApplication">
    <configuration name="SqlQueryBuilder" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.rutong.framework.utils.SqlQueryBuilder" />
      <module name="ClothingMgr" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.rutong.framework.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="StockProfitCalculator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.rutong.platform.client.controller.StockProfitCalculator" />
      <module name="ClothingMgr" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.rutong.platform.client.controller.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ClothingMgrApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="ClothingMgr" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.rutong.ClothingMgrApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.StockProfitCalculator" />
        <item itemvalue="Application.SqlQueryBuilder" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>$USER_HOME$/.subversion</configuration>
    <supportedVersion>125</supportedVersion>
    <option name="runUnderTerminal" value="true" />
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="$PROJECT_DIR$" />
          <option name="myCopyRoot" value="$PROJECT_DIR$" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="$PROJECT_DIR$" />
          <option name="myCopyRoot" value="$PROJECT_DIR$" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="64b99ec0-3378-4519-86aa-0bf3b1ee946b" name="Changes" comment="" />
      <created>1720503118963</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1720503118963</updated>
      <workItem from="1720503120149" duration="3360000" />
      <workItem from="1720507453368" duration="49000" />
      <workItem from="1720507503674" duration="26030000" />
      <workItem from="1722820975333" duration="1037000" />
      <workItem from="1723113008005" duration="7914000" />
      <workItem from="1723191515221" duration="1633000" />
      <workItem from="1723426302701" duration="16189000" />
      <workItem from="1724634401769" duration="19802000" />
      <workItem from="1725245153268" duration="4824000" />
      <workItem from="1725330876895" duration="6869000" />
      <workItem from="1725370541120" duration="22000" />
      <workItem from="1725414112921" duration="8686000" />
      <workItem from="1725862173969" duration="21047000" />
      <workItem from="1729837010847" duration="16825000" />
      <workItem from="1730424016152" duration="13978000" />
      <workItem from="1730612751578" duration="5000" />
      <workItem from="1730697752790" duration="93370000" />
      <workItem from="1731585020625" duration="21000" />
      <workItem from="1731660444532" duration="17423000" />
      <workItem from="1732009824231" duration="13346000" />
      <workItem from="1732242048409" duration="13279000" />
      <workItem from="1732497316999" duration="2494000" />
      <workItem from="1732674211287" duration="1159000" />
      <workItem from="1732676060328" duration="1194000" />
      <workItem from="1732685626222" duration="53856000" />
      <workItem from="1733202080694" duration="10235000" />
      <workItem from="1733274223943" duration="15151000" />
      <workItem from="1733361902462" duration="9318000" />
      <workItem from="1733448504617" duration="10142000" />
      <workItem from="1733488861758" duration="2535000" />
      <workItem from="1733714651854" duration="2138000" />
      <workItem from="1733793424928" duration="8298000" />
      <workItem from="1733965443779" duration="9370000" />
      <workItem from="1734333846503" duration="790000" />
      <workItem from="1734415817957" duration="6069000" />
      <workItem from="1734492884294" duration="718000" />
      <workItem from="1734780870355" duration="1792000" />
      <workItem from="1735612274537" duration="4269000" />
      <workItem from="1736130631706" duration="4337000" />
      <workItem from="1736322858317" duration="3122000" />
      <workItem from="1739235905993" duration="13677000" />
      <workItem from="1739328969106" duration="3137000" />
      <workItem from="1739337064439" duration="4751000" />
      <workItem from="1742178190974" duration="18034000" />
      <workItem from="1742873433239" duration="3000" />
      <workItem from="1743062590769" duration="6395000" />
      <workItem from="1743470159192" duration="10300000" />
      <workItem from="1743498535917" duration="4000" />
      <workItem from="1743556453463" duration="51000" />
      <workItem from="1743558686666" duration="55427000" />
      <workItem from="1744770020896" duration="2508000" />
      <workItem from="1744774754672" duration="63952000" />
      <workItem from="1745562807057" duration="6256000" />
      <workItem from="1745733538048" duration="1926000" />
      <workItem from="1745745435583" duration="699000" />
      <workItem from="1745833362307" duration="25095000" />
      <workItem from="1747012255392" duration="2944000" />
      <workItem from="1747017227000" duration="1314000" />
      <workItem from="1747047426971" duration="1342000" />
      <workItem from="1747214752607" duration="9764000" />
      <workItem from="1747831953988" duration="7000" />
      <workItem from="1747919111489" duration="2598000" />
      <workItem from="1748254571807" duration="5770000" />
      <workItem from="1748340591139" duration="3193000" />
      <workItem from="1748504302062" duration="3044000" />
      <workItem from="1748598065106" duration="7048000" />
      <workItem from="1749433156504" duration="8725000" />
      <workItem from="1750136761277" duration="4196000" />
      <workItem from="1751270585522" duration="2536000" />
      <workItem from="1751432788401" duration="1462000" />
      <workItem from="1752045934046" duration="2537000" />
      <workItem from="1752124136864" duration="8161000" />
      <workItem from="1752462065901" duration="7263000" />
      <workItem from="1752627870915" duration="2279000" />
      <workItem from="1753147435578" duration="2317000" />
      <workItem from="1753175131630" duration="3737000" />
      <workItem from="1753280357415" duration="631000" />
      <workItem from="1753411292688" duration="314000" />
      <workItem from="1753423741152" duration="24935000" />
      <workItem from="1753773168612" duration="7061000" />
      <workItem from="1753783838803" duration="6190000" />
      <workItem from="1753942414683" duration="2862000" />
      <workItem from="1754373684354" duration="14604000" />
      <workItem from="1755049632500" duration="5267000" />
      <workItem from="1755160616872" duration="6131000" />
      <workItem from="1755510102651" duration="975000" />
      <workItem from="1755567652823" duration="27327000" />
      <workItem from="1756084814120" duration="12677000" />
      <workItem from="1756259112112" duration="6000" />
      <workItem from="1756260300665" duration="3102000" />
      <workItem from="1756285924930" duration="25435000" />
      <workItem from="1756702995989" duration="7951000" />
      <workItem from="1756716742193" duration="31000" />
      <workItem from="1756716796930" duration="292000" />
      <workItem from="1756717124385" duration="10498000" />
    </task>
    <task id="LOCAL-00001" summary="初始">
      <created>1723453989036</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1723453989036</updated>
    </task>
    <task id="LOCAL-00002" summary="模块增加  查询条件为空判断">
      <created>1724828227028</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1724828227028</updated>
    </task>
    <task id="LOCAL-00003" summary="客户字段更新">
      <created>1725330926032</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1725330926032</updated>
    </task>
    <task id="LOCAL-00004" summary="客户运营模块完善功能">
      <created>1730944112029</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1730944112029</updated>
    </task>
    <task id="LOCAL-00005" summary="增加根据对象查询条件">
      <created>1731034967028</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1731034967028</updated>
    </task>
    <task id="LOCAL-00006" summary="运营模块提交">
      <created>1732701189045</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1732701189045</updated>
    </task>
    <task id="LOCAL-00007" summary="运营模块提交">
      <created>1732711369029</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1732711369029</updated>
    </task>
    <task id="LOCAL-00008" summary="运营模块提交">
      <created>1732711379029</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1732711379029</updated>
    </task>
    <task id="LOCAL-00009" summary="增加财务模块  以及客户端接口">
      <created>1733274285033</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1733274285033</updated>
    </task>
    <task id="LOCAL-00010" summary="产线亮灯处理">
      <created>1733361978029</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1733361978029</updated>
    </task>
    <task id="LOCAL-00011" summary="增加文件下载  以及对接客户端功能">
      <created>1734333922055</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1734333922055</updated>
    </task>
    <task id="LOCAL-00012" summary="增加库存管理以及任务单信息 并对接接口功能">
      <created>1744854747036</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1744854747036</updated>
    </task>
    <task id="LOCAL-00013" summary="模版提交">
      <created>1744955985028</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1744955985028</updated>
    </task>
    <task id="LOCAL-00014" summary="bug修复 增加 交接单判断">
      <created>1744964389130</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1744964389130</updated>
    </task>
    <task id="LOCAL-00015" summary="导出实体更新">
      <created>1745205538026</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1745205538026</updated>
    </task>
    <task id="LOCAL-00016" summary="增加微信用户认证信息">
      <created>1745565388031</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1745565388031</updated>
    </task>
    <task id="LOCAL-00017" summary="增加根据手机号查询">
      <created>1745565904025</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1745565904025</updated>
    </task>
    <task id="LOCAL-00018" summary="导入模版更改">
      <created>1745566385036</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1745566385036</updated>
    </task>
    <task id="LOCAL-00019" summary="时间日期转换工具">
      <created>1745566799031</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1745566799031</updated>
    </task>
    <task id="LOCAL-00020">
      <created>1745733567025</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1745733567025</updated>
    </task>
    <task id="LOCAL-00021" summary="增加继承类 字段获取">
      <created>1745734802029</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1745734802029</updated>
    </task>
    <task id="LOCAL-00022" summary="增加分拣记录模块">
      <created>1747123991039</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1747123991040</updated>
    </task>
    <task id="LOCAL-00023" summary="增加导入服装排序字段">
      <created>1752628357039</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1752628357039</updated>
    </task>
    <task id="LOCAL-00024" summary="增加客户服务查询数据客户ID">
      <created>1753425501028</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1753425501028</updated>
    </task>
    <task id="LOCAL-00025" summary="导出excel 统计信息 接口bug修复">
      <created>1753752042030</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1753752042030</updated>
    </task>
    <task id="LOCAL-00026" summary="运营模块提交">
      <created>1755153657029</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1755153657029</updated>
    </task>
    <task id="LOCAL-00027" summary="洗涤记录查询修复 根据原有交接记录表查询">
      <created>1755593429029</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1755593429029</updated>
    </task>
    <task id="LOCAL-00028" summary="分拣记录导出问题修复">
      <created>1755596135034</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1755596135034</updated>
    </task>
    <task id="LOCAL-00029" summary="分拣重复问题修复 并发">
      <created>1756194686188</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1756194686188</updated>
    </task>
    <task id="LOCAL-00030" summary="批量打印问题解决 结算速度优化">
      <created>1756368954025</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1756368954025</updated>
    </task>
    <task id="LOCAL-00031" summary="修改导出分拣导出明细问题 导入数据不对修复 以及打印排序问题">
      <created>1756448155028</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1756448155028</updated>
    </task>
    <task id="LOCAL-00032" summary="分拣去重判定为1天">
      <created>1756708650030</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1756708650030</updated>
    </task>
    <option name="localTasksCounter" value="33" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="增加根据对象查询条件" />
    <MESSAGE value="增加财务模块  以及客户端接口" />
    <MESSAGE value="产线亮灯处理" />
    <MESSAGE value="增加文件下载  以及对接客户端功能" />
    <MESSAGE value="增加库存管理以及任务单信息 并对接接口功能" />
    <MESSAGE value="模版提交" />
    <MESSAGE value="bug修复 增加 交接单判断" />
    <MESSAGE value="导出实体更新" />
    <MESSAGE value="增加微信用户认证信息" />
    <MESSAGE value="增加根据手机号查询" />
    <MESSAGE value="导入模版更改" />
    <MESSAGE value="时间日期转换工具" />
    <MESSAGE value="增加继承类 字段获取" />
    <MESSAGE value="增加分拣记录模块" />
    <MESSAGE value="增加导入服装排序字段" />
    <MESSAGE value="增加客户服务查询数据客户ID" />
    <MESSAGE value="导出excel 统计信息 接口bug修复" />
    <MESSAGE value="运营模块提交" />
    <MESSAGE value="洗涤记录查询修复 根据原有交接记录表查询" />
    <MESSAGE value="分拣记录导出问题修复" />
    <MESSAGE value="查询结果排序 解决并发问题导致分拣记录重复" />
    <MESSAGE value="分拣重复问题修复 并发" />
    <MESSAGE value="批量打印问题解决 结算速度优化" />
    <MESSAGE value="修改导出分拣导出明细问题 导入数据不对修复 以及打印排序问题" />
    <MESSAGE value="分拣去重判定为1天" />
    <option name="LAST_COMMIT_MESSAGE" value="分拣去重判定为1天" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/app/controller/UnauthController.java</url>
          <line>52</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java</url>
          <line>220</line>
          <option name="timeStamp" value="86" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java</url>
          <line>243</line>
          <option name="timeStamp" value="88" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java</url>
          <line>223</line>
          <option name="timeStamp" value="89" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java</url>
          <line>228</line>
          <option name="timeStamp" value="90" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java</url>
          <line>231</line>
          <option name="timeStamp" value="91" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/EmployeeProductConfigurationService.java</url>
          <line>233</line>
          <option name="timeStamp" value="92" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/listener/ChangeTrackingEntityListener.java</url>
          <line>32</line>
          <option name="timeStamp" value="93" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/ChangeTrackingService.java</url>
          <line>38</line>
          <option name="timeStamp" value="94" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/ChangeTrackingService.java</url>
          <line>62</line>
          <option name="timeStamp" value="95" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/ChangeTrackingService.java</url>
          <line>114</line>
          <option name="timeStamp" value="96" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/rutong/platform/client/service/ChangeTrackingService.java</url>
          <line>299</line>
          <option name="timeStamp" value="98" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint type="python-exception">
          <properties notifyOnTerminate="true" exception="BaseException">
            <option name="notifyOnTerminate" value="true" />
          </properties>
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.rutong.framework.core.jpa.SortCondition" memberName="direction" />
        <PinnedItemInfo parentTag="com.rutong.framework.core.jpa.SortCondition" memberName="property" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/ClothingMgr$ClothingMgrApplication.ic" NAME="ClothingMgrApplication Coverage Results" MODIFIED="1745563939943" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
</project>