package com.rutong.platform.client.service;

import com.rutong.platform.client.entity.EmployeeProductConfigurationHistory;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.utils.FilterConditionBuilder;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.sys.entity.SysUser;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;

@Service
public class EmployeeProductConfigurationHistoryService extends BaseService<EmployeeProductConfigurationHistory> {

    public EmployeeProductConfigurationHistory selectUserByPhone(String phoneNumber) {
        return dao.findByPropertyFirst(EmployeeProductConfigurationHistory.class, EmployeeProductConfigurationHistory.EMPLOYEE_PHONE, phoneNumber);
    }

    /**
     * 根据RFID标签号查询历史记录
     *
     * @param rfidTagNumber RFID标签号
     * @return 历史记录列表
     */
    public List<EmployeeProductConfigurationHistory> findByRfidTagNumber(String rfidTagNumber) {
        return dao.findByProperty(EmployeeProductConfigurationHistory.class,
                EmployeeProductConfigurationHistory.FIELD_COL_RFID_TAG_NUMBER, rfidTagNumber);
    }

    /**
     * 根据RFID标签号查询历史记录并排序
     *
     * @param rfidTagNumber RFID标签号
     * @param sorts         排序条件
     * @return 历史记录列表
     */
    public List<EmployeeProductConfigurationHistory> findByRfidTagNumber(String rfidTagNumber, List<SortCondition> sorts) {
        List<FilterCondition> filters = new ArrayList<>();
        FilterCondition filter = new FilterCondition();
        filter.setProperty(EmployeeProductConfigurationHistory.FIELD_COL_RFID_TAG_NUMBER);
        filter.setOperator(FilterCondition.EQ);
        filter.setValue(rfidTagNumber);
        filters.add(filter);
        return dao.findAll(EmployeeProductConfigurationHistory.class, null, filters, sorts);
    }

    /**
     * 根据父ID查询历史记录
     *
     * @param parentId 父ID
     * @return 历史记录列表
     */
    public List<EmployeeProductConfigurationHistory> findByParentId(String parentId) {
        return dao.findByProperty(EmployeeProductConfigurationHistory.class, "parentId", parentId);
    }

    /**
     * 根据父ID查询历史记录并按创建时间降序排序
     *
     * @param parentId 父ID
     * @return 历史记录列表
     */
    public List<Map<String, Object>> findAllRelatedHistoryByParentId(String parentId) {
        String sql = "SELECT * FROM employee_product_configuration_history WHERE parent_id = ?0 ORDER BY create_time DESC";
        return dao.executeSQLQuery(sql, parentId);
    }

    /**
     * 通过SQL直接查询历史记录
     *
     * @param rfidTagNumber RFID标签号
     * @return 查询结果
     */
    public List<Map<String, Object>> findHistoryByRfidTagNumberSQL(String rfidTagNumber) {
        String sql = "SELECT * FROM employee_product_configuration_history WHERE rfid_tag_number = ?0";
        return dao.executeSQLQuery(sql, rfidTagNumber);
    }

    /**
     * 获取表结构
     *
     * @return 表结构信息
     */
    public List<Map<String, Object>> getTableStructure() {
        String sql = "SHOW COLUMNS FROM employee_product_configuration_history";
        return dao.executeSQLQuery(sql);
    }

    /**
     * 查询与RFID标签相关的所有历史记录（包括新值、旧值）
     *
     * @param rfidTagNumber RFID标签号
     * @return 查询结果
     */
    public List<Map<String, Object>> findAllRelatedHistoryByRfidTagNumber(String rfidTagNumber) {
        String sql = "SELECT * FROM employee_product_configuration_history WHERE rfid_tag_number = ?0 ORDER BY create_time DESC";
        return dao.executeSQLQuery(sql, rfidTagNumber);
    }


    /**
     * 异步批量保存历史记录
     * @param historyList 历史记录列表
     */
    public void asyncBatchSaveHistory(List<EmployeeProductConfigurationHistory> historyList) {
        if (historyList == null || historyList.isEmpty()) {
            return;
        }

        // 使用异步方式保存，避免阻塞主线程
        new Thread(() -> {
            saveAll(historyList);
//            batchSaveHistory(historyList);
        }).start();
    }

}
