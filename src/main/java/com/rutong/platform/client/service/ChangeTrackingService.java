package com.rutong.platform.client.service;

import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.annotation.ChangeTracking;
import com.rutong.platform.client.annotation.FieldTracking;
import com.rutong.platform.client.entity.EmployeeProductConfiguration;
import com.rutong.platform.client.entity.EmployeeProductConfigurationHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

/**
 * 变更追踪服务
 * 提供手动变更追踪功能，用于特殊场景
 */
@Service
public class ChangeTrackingService {

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private EmployeeProductConfigurationHistoryService historyService;

    /**
     * 手动追踪实体变更
     *
     * @param newEntity 新实体
     * @param entityId  实体ID
     */
    public void trackChanges(EmployeeProductConfiguration newEntity, String entityId) {
        try {
            // 获取原始实体
            EmployeeProductConfiguration originalEntity = employeeProductConfigurationService.findById(entityId);
            if (originalEntity == null) {
                return;
            }

            // 检测变更
            List<EmployeeProductConfigurationHistory> historyList = detectChanges(newEntity, originalEntity);

            if (!historyList.isEmpty()) {
                new Thread(() -> {
                    historyService.saveAll(historyList);
                }).start();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 批量追踪实体变更
     *
     * @param entities 实体列表（包含ID）
     */
    public void batchTrackChanges(List<EmployeeProductConfiguration> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        List<EmployeeProductConfigurationHistory> allHistoryList = new ArrayList<>();

        for (EmployeeProductConfiguration entity : entities) {
            if (entity.getId() == null) {
                continue;
            }

            try {
                // 获取原始实体
                EmployeeProductConfiguration originalEntity = employeeProductConfigurationService.findById(entity.getId());
                if (originalEntity == null) {
                    continue;
                }

                // 检测变更
                List<EmployeeProductConfigurationHistory> historyList = detectChanges(entity, originalEntity);
                allHistoryList.addAll(historyList);

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (!allHistoryList.isEmpty()) {
            // 异步批量保存历史记录
            historyService.asyncBatchSaveHistory(allHistoryList);
        }
    }

    /**
     * 检测两个实体之间的变更
     */
    private List<EmployeeProductConfigurationHistory> detectChanges(
            EmployeeProductConfiguration newEntity,
            EmployeeProductConfiguration originalEntity) {

        List<EmployeeProductConfigurationHistory> historyList = new ArrayList<>();

        if (newEntity == null || originalEntity == null) {
            return historyList;
        }

        Class<?> entityClass = newEntity.getClass();
        ChangeTracking tracking = entityClass.getAnnotation(ChangeTracking.class);
        if (tracking == null) {
            return historyList;
        }

        Set<String> ignoreFields = new HashSet<>(Arrays.asList(tracking.ignoreFields()));

        // 获取当前登录用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Date currentTime = new Date();

        // 获取所有字段
        List<Field> allFields = getAllFields(entityClass);

        for (Field field : allFields) {
            String fieldName = field.getName();

            // 跳过忽略的字段
            if (ignoreFields.contains(fieldName)) {
                continue;
            }

            // 检查字段是否标记为忽略
            FieldTracking fieldTracking = field.getAnnotation(FieldTracking.class);
            if (fieldTracking != null && fieldTracking.ignore()) {
                continue;
            }

            try {
                field.setAccessible(true);
                Object newValue = field.get(newEntity);
                Object oldValue = field.get(originalEntity);

                // 检查值是否发生变更
                if (!Objects.equals(newValue, oldValue)) {
                    String displayName = getFieldDisplayName(field, fieldTracking);

                    // 创建历史记录
                    EmployeeProductConfigurationHistory history = createHistoryRecord(originalEntity);
                    history.setChangeType(displayName + "变更");
                    history.setOldValue(valueToString(oldValue));
                    history.setNewValue(valueToString(newValue));

                    // 设置操作信息
                    if (loginUser != null) {
                        history.setOperator(loginUser.getUsername());
                        history.setCreateBy(loginUser.getUsername());
                        history.setOwnerDeptId(loginUser.getDeptId());
                        history.setOwnerUserId(loginUser.getUserId());
                    }
                    history.setCreateTime(currentTime);

                    historyList.add(history);
                }
            } catch (Exception e) {
                // 忽略无法访问的字段
            }
        }

        return historyList;
    }

    /**
     * 获取类的所有字段，包括父类字段
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }

        return fields;
    }

    /**
     * 获取字段显示名称
     */
    private String getFieldDisplayName(Field field, FieldTracking fieldTracking) {
        if (fieldTracking != null && !fieldTracking.displayName().isEmpty()) {
            return fieldTracking.displayName();
        }

        // 使用默认的字段名映射
        return getDefaultDisplayName(field.getName());
    }

    /**
     * 获取默认的字段显示名称
     */
    private String getDefaultDisplayName(String fieldName) {
        Map<String, String> displayNames = new HashMap<>();
        displayNames.put("contractNumber", "合同编号");
        displayNames.put("enterpriseName", "客户名称");
        displayNames.put("employeeName", "员工姓名");
        displayNames.put("employeePhone", "员工电话");
        displayNames.put("department", "部门");
        displayNames.put("subDepartment", "分部门");
        displayNames.put("workWearConfigurationCategory", "工作服品类");
        displayNames.put("configurationCategory", "配置品类");
        displayNames.put("productModelNumber", "产品款号");
        displayNames.put("productColor", "产品颜色");
        displayNames.put("logoPosition", "LOGO位置");
        displayNames.put("productSpecification", "产品规格");
        displayNames.put("protocolConfigurationQuantity", "协议各品类配置数量");
        displayNames.put("employeeId", "员工企业工号");
        displayNames.put("rfidTagNumberWork", "工作服RFID标签编码");
        displayNames.put("rfidTagNumber", "RFID标签号");
        displayNames.put("productSerialNumber", "员工产品序列号");
        displayNames.put("lockerModel", "存衣柜协议配置型号");
        displayNames.put("lockerQuantity", "协议存衣柜配置数量");
        displayNames.put("lockerSerialNumber", "员工更衣柜序列号");
        displayNames.put("sortingBoxNumber", "检品分拣柜号");
        displayNames.put("dirtyLockerModel", "脏衣柜协议配置型号");
        displayNames.put("dirtyLockerQuantity", "协议脏衣柜配置数量");
        displayNames.put("dirtyLockerSerialNumber", "脏衣柜序列号");
        displayNames.put("protocolUsageMonths", "协议配置品类使用月份");
        displayNames.put("weeklySchedule", "协议每周作息时间");
        displayNames.put("weeklyWashDate", "协议每周换洗日期");
        displayNames.put("weeklyWashFrequency", "协议每周换洗次数");
        displayNames.put("productWashInfo", "配置产品洗涤信息");
        displayNames.put("singleWashRentalFee", "协议单次清洗租赁费");
        displayNames.put("weeklyWashRentalFee", "协议每周清洗租赁费");
        displayNames.put("clothingSettlementPrice", "协议服装结算价");
        displayNames.put("clothingResidualValue", "本月服装余值");
        displayNames.put("clothingLossCount", "客方本月服装丢失次数");
        displayNames.put("clothingLossFee", "客方本月服装丢失费");
        displayNames.put("monthlyTotalCost", "月合计费用");
        displayNames.put("protocolStartDate", "协议启用日期");
        displayNames.put("protocolEndDate", "协议截止日期");
        displayNames.put("resignationDate", "离职日期");
        displayNames.put("attributes", "属性");
        displayNames.put("status", "状态");
        displayNames.put("serviceType", "服务类型");

        return displayNames.getOrDefault(fieldName, fieldName);
    }

    /**
     * 创建历史记录基础对象
     */
    private EmployeeProductConfigurationHistory createHistoryRecord(EmployeeProductConfiguration entity) {
        EmployeeProductConfigurationHistory history = new EmployeeProductConfigurationHistory();

        // 复制所有字段到历史记录
        BeanUtils.copyProperties(entity, history);

        // 设置历史记录特有字段
        history.setParentId(entity.getId());
        history.setId(null);

        return history;
    }

    /**
     * 将值转换为字符串
     */
    private String valueToString(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Date) {
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format((Date) value);
        }
        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).toPlainString();
        }
        return value.toString();
    }

    /**
     * 批量导入时的变更追踪 - 专门处理导入场景
     * @param importEntities 导入的实体列表
     */
    public void batchImportTrackChanges(List<EmployeeProductConfiguration> importEntities) {
        if (importEntities == null || importEntities.isEmpty()) {
            return;
        }

        System.out.println("开始批量导入变更追踪，共 " + importEntities.size() + " 条记录");
        List<EmployeeProductConfigurationHistory> allHistoryList = new ArrayList<>();

        for (EmployeeProductConfiguration importEntity : importEntities) {
            if (importEntity.getId() == null) {
                continue; // 新增记录，跳过
            }

            try {
                // 在导入前，先获取数据库中的原始数据
                EmployeeProductConfiguration originalEntity = employeeProductConfigurationService.findById(importEntity.getId());
                if (originalEntity == null) {
                    System.out.println("导入记录ID " + importEntity.getId() + " 在数据库中不存在，跳过变更追踪");
                    continue;
                }

                // 检测变更
                List<EmployeeProductConfigurationHistory> historyList = detectChanges(importEntity, originalEntity);
                if (!historyList.isEmpty()) {
                    System.out.println("导入记录ID " + importEntity.getId() + " 检测到 " + historyList.size() + " 个字段变更");
                    allHistoryList.addAll(historyList);
                }

            } catch (Exception e) {
                System.err.println("处理导入记录ID " + importEntity.getId() + " 的变更追踪失败: " + e.getMessage());
                e.printStackTrace();
            }
        }

        if (!allHistoryList.isEmpty()) {
            System.out.println("批量导入变更追踪完成，共生成 " + allHistoryList.size() + " 条历史记录，开始异步保存");
            // 异步批量保存历史记录
            historyService.asyncBatchSaveHistory(allHistoryList);
        } else {
            System.out.println("批量导入未检测到任何变更");
        }
    }
}
