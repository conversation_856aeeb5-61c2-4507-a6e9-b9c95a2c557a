package com.rutong.platform.client.controller;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.constant.Constants;
import com.rutong.framework.constant.GlobalContsant;
import com.rutong.framework.core.interceptor.RequestList;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.core.annotation.LogDesc;
import com.rutong.framework.core.redis.RedisCache;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.Global;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.client.excel.EmployeeProductConfigurationExcel;
import com.rutong.platform.client.excel.EmployeeProductConfigurationListener;
import com.rutong.platform.client.excel.ExpenseSettlementItemExcel;
import com.rutong.platform.client.service.ClientInfoService;
import com.rutong.platform.client.service.EmployeeProductConfigurationDetailService;
import com.rutong.platform.client.service.EmployeeProductConfigurationHistoryService;
import com.rutong.platform.client.service.EmployeeProductConfigurationService;
import com.rutong.platform.client.service.ChangeTrackingService;
import com.rutong.platform.client.util.ExportUtil;
import com.rutong.platform.client.util.GenerateXML;
import com.rutong.platform.common.controller.CrudController;
import com.rutong.platform.common.entity.BaseEntity;
import com.rutong.platform.common.service.BaseService;
import com.rutong.platform.wms.entity.WmsDeliveryDetail;
import com.rutong.platform.wms.service.WmsDeliveryDetailService;
import org.hibernate.Session;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.rutong.framework.security.utils.DataAuthUtil;

import java.math.BigDecimal;

@RestController
@RequestMapping("/client/employeeProductConfiguration")
@LogDesc(title = "员工产品配置")
public class EmployeeProductConfigurationController extends CrudController<EmployeeProductConfiguration> {

    // 日期格式常量
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final java.text.SimpleDateFormat dateFormatter = new java.text.SimpleDateFormat(DATE_FORMAT);

    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;

    @Autowired
    private EmployeeProductConfigurationHistoryService employeeProductConfigurationHistoryService;

    @Autowired
    private WmsDeliveryDetailService wmsDeliveryDetailService;

    @Autowired
    private ClientInfoService clientInfoService;

    @Autowired
    private com.rutong.framework.core.dao.Dao dao;

    @Autowired
    private EmployeeProductConfigurationDetailService employeeProductConfigurationDetailService;

    @Autowired
    private ChangeTrackingService changeTrackingService;

    @Override
    public BaseService<EmployeeProductConfiguration> getService() {
        return employeeProductConfigurationService;
    }

    /**
     * 新增数据
     *
     * @param entity
     * @return
     */
    @Override
    public ResultBean save(EmployeeProductConfiguration entity) {

        ClientInfo byContractNumber = clientInfoService.findByContractNumber(entity.getContractNumber());

        EmployeeProductConfiguration allByObject = employeeProductConfigurationService.byRfid(entity.getRfidTagNumber());
        if (allByObject != null) {
            throw new ExcelAnalysisException("RFID标签信息：" + entity.getRfidTagNumber() + "，已存在无法重复添加");
        }

        EmployeeProductConfiguration employeeProductConfiguration = employeeProductConfigurationService.byProductSerialNumber(entity.getProductSerialNumber());
        if (employeeProductConfiguration != null) {
            throw new ExcelAnalysisException("产品序列号：" + entity.getProductSerialNumber() + "，已存在无法重复添加");
        }

        entity.setEnterpriseName(byContractNumber.getEnterpriseName());
        entity.setEnterpriseId(byContractNumber.getId());
        //employeeProductConfiguration.setContractNumber(contractNumber);
        employeeProductConfigurationService.save(entity);

        //新增------冬夏装分别配置列表
        //通过前端传过来的JSONList列表tableData获取到EmployeeProductConfigurationDetail中的字段，生成employeeProductConfigurationDetails

        List<EmployeeProductConfigurationDetail> employeeProductConfigurationDetails = JSONArray.parseArray(entity.getTableData(), EmployeeProductConfigurationDetail.class);
        if (employeeProductConfigurationDetails != null && !employeeProductConfigurationDetails.isEmpty()) {
            for (EmployeeProductConfigurationDetail detail : employeeProductConfigurationDetails) {
                // 为子表设置ID
                detail.setId(java.util.UUID.randomUUID().toString());
                // 设置关联关系,EmployeeProductConfigurationId为主表的主键id
                detail.setEmployeeProductConfigurationId(entity.getId());
                // 使用安全保存方法
                employeeProductConfigurationDetailService.safeSave(detail);
            }
        }
        return ResultBean.success(entity);
    }

    /**
     * 获取员工产品配置
     *
     * @param gridParams
     * @param filters
     * @param sorts
     * @return
     */
    @PostMapping(value = "/fetchdataGroup.do")
    public PageInfo<EmployeeProductConfiguration> fetchdataGroup(GridParams gridParams,
                                                                 @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                                 @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
            sorts.add(sortCondition);
        }
        return employeeProductConfigurationService.fetchdataGroup(gridParams, filters, sorts);
    }


    /**
     * 查看服务记录与查询列表
     *
     * @param gridParams
     * @param filters
     * @param sorts
     * @return
     */

    @Override
    @PostMapping(value = "/fetchdata.do")
    public PageInfo<EmployeeProductConfiguration> findAllByPage(GridParams gridParams,
                                                                @RequestList(clazz = FilterCondition.class) List<FilterCondition> filters,
                                                                @RequestList(clazz = SortCondition.class) List<SortCondition> sorts) {
        //排序
        // 如果没有排序条件，默认按创建时间倒序排序
        if (sorts.size() == 0) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setDirection(SortCondition.SORT_DESC);
            sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);

            //按照记录与查询和服装打印按照Excel表格导入顺序显示
            SortCondition sortCondition3 = new SortCondition();
            sortCondition3.setDirection(SortCondition.SORT_ASC);
            sortCondition3.setProperty("serialNumber");
            sorts.add(sortCondition3);
        }
        System.out.println("接收到的筛选条件: " + filters);
        // 检查是否包含日期范围筛选条件并处理，同时收集其他过滤条件
        List<FilterCondition> processedFilters = new ArrayList<>();
        // 日期处理过滤标记
        boolean dateProcessed = false;
        try {
            for (FilterCondition filter : filters) {
                // 检查是否是日期范围条件
                if (("protocolStartDate".equals(filter.getProperty()) || "protocolEndDate".equals(filter.getProperty()))
                        && "between".equals(filter.getOperator())) {

                    // 避免重复处理日期条件
                    if (dateProcessed) {
                        continue;
                    }

                    // 安全获取日期范围值
                    Object value = filter.getValue();
                    if (value == null) {
                        System.out.println("日期条件值为null，跳过处理");
                        continue;
                    }

                    // 处理数组值
                    String startDateStr = null;
                    String endDateStr = null;

                    if (value instanceof Object[]) {
                        Object[] dateRange = (Object[]) value;
                        // 安全检查数组长度
                        if (dateRange.length >= 2 && dateRange[0] != null && dateRange[1] != null) {
                            startDateStr = dateRange[0].toString().replaceAll("^\"|\"$", "");
                            endDateStr = dateRange[1].toString().replaceAll("^\"|\"$", "");
                        } else {
                            System.out.println("日期范围数组不完整或包含null值，跳过处理");
                            continue;
                        }
                    } else if (value instanceof String) {
                        // 如果是字符串，尝试解析为日期范围
                        String strValue = (String) value;
                        if (strValue.contains(",")) {
                            String[] parts = strValue.split(",");
                            if (parts.length >= 2) {
                                startDateStr = parts[0].replaceAll("^\"|\"$", "");
                                endDateStr = parts[1].replaceAll("^\"|\"$", "");
                            }
                        }
                    } else if (value instanceof List) {
                        // 如果是列表，尝试获取日期值
                        List<?> listValue = (List<?>) value;
                        if (listValue.size() >= 2 && listValue.get(0) != null && listValue.get(1) != null) {
                            startDateStr = listValue.get(0).toString().replaceAll("^\"|\"$", "");
                            endDateStr = listValue.get(1).toString().replaceAll("^\"|\"$", "");
                        }
                    }
                    // 检查日期字符串是否有效
                    if (startDateStr == null || endDateStr == null || startDateStr.isEmpty() || endDateStr.isEmpty()) {
                        System.out.println("无法解析日期范围值，跳过处理");
                        continue;
                    }
                    System.out.println("成功提取日期范围: " + startDateStr + " 至 " + endDateStr);
                    try {
                        // 将字符串日期转换为Date对象
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        Date startDate = sdf.parse(startDateStr);
                        Date endDate = sdf.parse(endDateStr);

                        // 根据筛选字段确定应用逻辑
                        String propertyName = filter.getProperty();
                        System.out.println("处理日期字段: " + propertyName);

                        if ("protocolStartDate".equals(propertyName)) {
                            // 协议启用日期筛选：查询协议启用日期在范围内的记录
                            // 协议启用日期 >= 查询开始日期
                            FilterCondition startDateGEFilter = new FilterCondition();
                            startDateGEFilter.setProperty("protocolStartDate");
                            startDateGEFilter.setOperator(FilterCondition.GE);
                            startDateGEFilter.setValue(startDate);
                            processedFilters.add(startDateGEFilter);

                            // 协议启用日期 <= 查询结束日期
                            FilterCondition startDateLEFilter = new FilterCondition();
                            startDateLEFilter.setProperty("protocolStartDate");
                            startDateLEFilter.setOperator(FilterCondition.LE);
                            startDateLEFilter.setValue(endDate);
                            processedFilters.add(startDateLEFilter);

                            System.out.println("已添加协议启用日期筛选条件: 启用日期在 " + startDate + " 和 " + endDate + " 之间");
                        } else if ("protocolEndDate".equals(propertyName)) {
                            // 协议截止日期筛选
                            // 协议截止日期 >= 查询开始日期
                            FilterCondition endDateGEFilter = new FilterCondition();
                            endDateGEFilter.setProperty("protocolEndDate");
                            endDateGEFilter.setOperator(FilterCondition.GE);
                            endDateGEFilter.setValue(startDate);
                            processedFilters.add(endDateGEFilter);

                            // 协议截止日期 <= 查询结束日期
                            FilterCondition endDateLEFilter = new FilterCondition();
                            endDateLEFilter.setProperty("protocolEndDate");
                            endDateLEFilter.setOperator(FilterCondition.LE);
                            endDateLEFilter.setValue(endDate);
                            processedFilters.add(endDateLEFilter);

                            System.out.println("已添加协议截止日期筛选条件: 截止日期在 " + startDate + " 和 " + endDate + " 之间");
                        }
                    } catch (ParseException e) {
                        System.err.println("日期格式转换错误: " + e.getMessage());
                        e.printStackTrace();
                    }

                    // 标记日期条件已处理
                    dateProcessed = true;
                } else {
                    // 对于非日期条件，直接添加到处理后的过滤器列表
                    processedFilters.add(filter);
                }
            }
        } catch (Exception e) {
            System.err.println("处理筛选条件时出错: " + e.getMessage());
            e.printStackTrace();
            // 出错时使用原始过滤条件
            processedFilters = filters;
        }

        System.out.println("处理后的筛选条件: " + processedFilters);

        // 增加数据权限
        DataAuthUtil.setDataAuth(processedFilters);

        // 使用处理后的条件进行查询
        return employeeProductConfigurationService.findAllByPage(gridParams, processedFilters, sorts);
    }

    /**
     * 更新服务记录与查询数据 - 使用手动变更追踪避免JPA监听器问题
     *
     * @param entity 实体数据
     * @return
     */
    @PostMapping(value = "/update.do")
    public ResultBean update(EmployeeProductConfiguration entity) {
        long startTime = System.currentTimeMillis();

        if (entity.getId() == null) {
            return ResultBean.error("记录ID不能为空", 400);
        }

        try {
            // 使用手动变更追踪，避免JPA监听器在批量导入时的问题
            changeTrackingService.trackChanges(entity, entity.getId());

            // 使用service层的update方法更新实体
            EmployeeProductConfiguration updatedEntity = employeeProductConfigurationService.update(entity);

            long endTime = System.currentTimeMillis();
            System.out.println("实体更新完成，耗时：" + (endTime - startTime) + "毫秒");

            // 更新------冬夏装分别配置列表
            // 处理tableData字段的更新
            if (entity.getTableData() != null && !entity.getTableData().trim().isEmpty()) {
                // 先删除旧的详情记录
                employeeProductConfigurationDetailService.deleteByEmployeeProductConfigurationId(entity.getId());

                // 解析新的tableData并保存
                List<EmployeeProductConfigurationDetail> employeeProductConfigurationDetails = JSONArray.parseArray(entity.getTableData(), EmployeeProductConfigurationDetail.class);
                if (employeeProductConfigurationDetails != null && !employeeProductConfigurationDetails.isEmpty()) {
                    for (EmployeeProductConfigurationDetail detail : employeeProductConfigurationDetails) {
                        // 设置关联关系,EmployeeProductConfigurationId为主表的主键id
                        detail.setEmployeeProductConfigurationId(entity.getId());

                        // 为新记录生成新的ID（避免detached entity问题）
                        detail.setId(java.util.UUID.randomUUID().toString());

                        // 使用安全保存方法，避免detached entity问题
                        employeeProductConfigurationDetailService.safeSave(detail);
                    }
                }
            }

            return ResultBean.success(updatedEntity);

        } catch (Exception e) {
            System.err.println("更新实体失败: " + e.getMessage());
            e.printStackTrace();
            return ResultBean.error("更新失败: " + e.getMessage(), 500);
        }
    }

    /**
     * 批量更新服务记录 - 使用高效变更追踪
     *
     * @param entities 实体列表
     * @return
     */
    @PostMapping(value = "/batchUpdate.do")
    public ResultBean batchUpdate(@RequestBody List<EmployeeProductConfiguration> entities) {
        long startTime = System.currentTimeMillis();

        if (entities == null || entities.isEmpty()) {
            return ResultBean.error("更新数据不能为空", 400);
        }

        try {
            // 使用批量变更追踪
            changeTrackingService.batchTrackChanges(entities);

            // 批量更新实体
            List<EmployeeProductConfiguration> updatedEntities = new ArrayList<>();
            for (EmployeeProductConfiguration entity : entities) {
                if (entity.getId() != null) {
                    EmployeeProductConfiguration updated = employeeProductConfigurationService.update(entity);
                    updatedEntities.add(updated);
                }
            }

            long endTime = System.currentTimeMillis();
            System.out.println("批量更新完成，共更新 " + updatedEntities.size() + " 条记录，耗时：" + (endTime - startTime) + "毫秒");

            return ResultBean.success(updatedEntities);

        } catch (Exception e) {
            System.err.println("批量更新失败: " + e.getMessage());
            e.printStackTrace();
            return ResultBean.error("批量更新失败: " + e.getMessage(), 500);
        }
    }

    /**
     * 检查字段变更并记录到历史表 - 已废弃，使用JPA自动追踪
     *
     * @param newEntity   新实体
     * @param oldEntity   旧实体
     * @param fieldName   字段名
     * @param displayName 显示名称
     */
    private void checkAndRecordFieldChange(EmployeeProductConfiguration newEntity, EmployeeProductConfiguration oldEntity,
                                           String fieldName, String displayName, List<EmployeeProductConfigurationHistory> historyList) {
        try {
            // 使用反射获取字段值
            String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
            System.out.println("尝试调用方法: " + methodName);

            // 检查方法是否存在
            java.lang.reflect.Method getMethod = null;
            Object newValue = null;
            Object oldValue = null;

            try {
                getMethod = EmployeeProductConfiguration.class.getMethod(methodName);

                // 调用get方法获取值
                newValue = getMethod.invoke(newEntity);
                oldValue = getMethod.invoke(oldEntity);
            } catch (NoSuchMethodException e) {
                System.out.println("方法 " + methodName + " 不存在，尝试使用字段直接访问");

                // 如果方法不存在，尝试直接访问字段
                try {
                    // 尝试在DTO中查找字段
                    java.lang.reflect.Field field = null;
                    try {
                        field = EmployeeProductConfiguration.class.getDeclaredField(fieldName);
                    } catch (NoSuchFieldException ex) {
                        // 如果在当前类中找不到，尝试在父类中查找
                        try {
                            field = EmployeeProductConfiguration.class.getSuperclass().getDeclaredField(fieldName);
                        } catch (NoSuchFieldException ex2) {
                            throw new Exception("字段 " + fieldName + " 在实体类及其父类中都不存在");
                        }
                    }
                    field.setAccessible(true);
                    newValue = field.get(newEntity);
                    oldValue = field.get(oldEntity);
                } catch (Exception fieldException) {
                    System.out.println("字段访问异常: " + fieldException.getMessage());
                    fieldException.printStackTrace();

                    // 尝试使用替代方法名
                    String alternativeMethodName = null;

                    // 尝试可能的别名
                    if (fieldName.equals("employeeId")) {
                        alternativeMethodName = "getEmployeeNumber";
                    } else if (fieldName.equals("protocolUsageMonths")) {
                        alternativeMethodName = "getLaundryUsingMonth";
                    } else if (fieldName.equals("weeklySchedule")) {
                        alternativeMethodName = "getWeekdayUsingInfo";
                    } else if (fieldName.equals("weeklyWashDate")) {
                        alternativeMethodName = "getWeekWashingDays";
                    } else if (fieldName.equals("weeklyWashFrequency")) {
                        alternativeMethodName = "getWeeklyChangeTimes";
                    } else if (fieldName.equals("singleWashRentalFee")) {
                        alternativeMethodName = "getOnceWashingFee";
                    } else if (fieldName.equals("weeklyWashRentalFee")) {
                        alternativeMethodName = "getWeeklyCleaningFee";
                    } else if (fieldName.equals("lockerSerialNumber")) {
                        alternativeMethodName = "getClothesNumber";
                    } else if (fieldName.equals("attributes")) {
                        alternativeMethodName = "getAttributeSet";
                    }

                    if (alternativeMethodName != null) {
                        try {
                            System.out.println("尝试使用替代方法: " + alternativeMethodName);
                            java.lang.reflect.Method altMethod = EmployeeProductConfiguration.class.getMethod(alternativeMethodName);
                            newValue = altMethod.invoke(newEntity);
                            oldValue = altMethod.invoke(oldEntity);
                        } catch (Exception altEx) {
                            System.out.println("替代方法调用失败: " + altEx.getMessage());
                            return;
                        }
                    } else {
                        return;
                    }
                }
            }

            // 特殊处理日期类型
            if (newValue instanceof Date || oldValue instanceof Date) {
                if (newValue == null && oldValue == null) {
                    // 两个都为null，视为没变化
                    return;
                }

                if (newValue == null || oldValue == null) {
                    // 一个为null，一个不为null，视为有变化
                    String newValueStr = newValue == null ? "" : dateFormatter.format(newValue);
                    String oldValueStr = oldValue == null ? "" : dateFormatter.format(oldValue);

                    // 记录变化
                    recordChange(oldEntity, displayName, oldValueStr, newValueStr, historyList);
                    return;
                }

                // 两个都不为null，比较日期内容
                Date newDate = (Date) newValue;
                Date oldDate = (Date) oldValue;

                // 如果新日期不等于旧日期
                if (!newDate.equals(oldDate)) {
                    // 将新日期格式化为字符串
                    String newValueStr = dateFormatter.format(newDate);
                    // 将旧日期格式化为字符串
                    String oldValueStr = dateFormatter.format(oldDate);

                    // 记录变化
                    recordChange(oldEntity, displayName, oldValueStr, newValueStr, historyList);
                }
                return;
            }

            // 特殊处理数字类型
            if (newValue instanceof Number || oldValue instanceof Number) {
                if (newValue == null && oldValue == null) {
                    // 两个都为null，视为没变化
                    return;
                }

                String newValueStr = newValue == null ? "0" : String.valueOf(newValue);
                String oldValueStr = oldValue == null ? "0" : String.valueOf(oldValue);

                // 去除尾部的.0
                if (newValueStr.endsWith(".0")) {
                    newValueStr = newValueStr.substring(0, newValueStr.length() - 2);
                }
                if (oldValueStr.endsWith(".0")) {
                    oldValueStr = oldValueStr.substring(0, oldValueStr.length() - 2);
                }

                System.out.println("检查数字字段 [" + displayName + "]: 原值=[" + oldValueStr + "], 新值=[" + newValueStr + "]");

                if (!newValueStr.equals(oldValueStr)) {
                    // 记录变化
                    recordChange(oldEntity, displayName, oldValueStr, newValueStr, historyList);
                }
                return;
            }

            // 默认字符串比较
            String newValueStr = newValue == null ? "" : String.valueOf(newValue);
            String oldValueStr = oldValue == null ? "" : String.valueOf(oldValue);

            System.out.println("检查字段 [" + displayName + "]: 原值=[" + oldValueStr + "], 新值=[" + newValueStr + "]");

            // 检查是否有变更
            if (!newValueStr.equals(oldValueStr)) {
                // 记录变化
                recordChange(oldEntity, displayName, oldValueStr, newValueStr, historyList);
            }
        } catch (Exception e) {
            System.out.println("检查字段 [" + displayName + "] 变更时发生异常: " + e.getMessage());
            System.out.println("异常类型: " + e.getClass().getName());
            System.out.println("异常栈信息:");
            e.printStackTrace();

            // 尝试使用Map方式访问属性
            try {
                java.lang.reflect.Method getAttributesMethod = EmployeeProductConfiguration.class.getMethod("getAttributes");
                if (getAttributesMethod != null) {
                    Map<String, Object> newAttributes = (Map<String, Object>) getAttributesMethod.invoke(newEntity);
                    Map<String, Object> oldAttributes = (Map<String, Object>) getAttributesMethod.invoke(oldEntity);

                    if (newAttributes != null && oldAttributes != null) {
                        Object newValue = newAttributes.get(fieldName);
                        Object oldValue = oldAttributes.get(fieldName);

                        String newValueStr = newValue == null ? "" : String.valueOf(newValue);
                        String oldValueStr = oldValue == null ? "" : String.valueOf(oldValue);

                        System.out.println("Map方式检查字段 [" + displayName + "]: 原值=[" + oldValueStr + "], 新值=[" + newValueStr + "]");

                        if (!newValueStr.equals(oldValueStr)) {
                            // 记录变化
                            recordChange(oldEntity, displayName, oldValueStr, newValueStr, historyList);
                        }
                    }
                }
            } catch (Exception ex) {
                System.out.println("Map方式访问字段失败: " + ex.getMessage());
            }
        }
    }

    /**
     * 记录字段变更
     */
    private void recordChange(EmployeeProductConfiguration oldEntity,
                              String displayName,
                              String oldValueStr,
                              String newValueStr,
                              List<EmployeeProductConfigurationHistory> historyList) {
        EmployeeProductConfigurationHistory history = createHistoryRecord(oldEntity);
        history.setChangeType(displayName + "变更");
        history.setOldValue(oldValueStr);
        history.setNewValue(newValueStr);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser!=null){
            history.setCreateBy(loginUser.getUsername());
            history.setOwnerDeptId(loginUser.getDeptId());
            history.setOwnerUserId(loginUser.getUserId());
        }

        // 保存历史记录
        //employeeProductConfigurationHistoryService.save(history);
        historyList.add(history);
        System.out.println("检测到" + displayName + "变更，已记录历史");
    }

    /**
     * 创建历史记录对象
     */
    private EmployeeProductConfigurationHistory createHistoryRecord(EmployeeProductConfiguration source) {
        EmployeeProductConfigurationHistory history = new EmployeeProductConfigurationHistory();
        BeanUtils.copyProperties(source, history);
        history.setParentId(source.getId());
        history.setId(null);

        // 手动设置创建时间和更新时间
        Date now = new Date();
        history.setCreateTime(now);
        history.setUpdateTime(now);

        // 设置操作人
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser != null) {
            history.setOperator(loginUser.getUsername());
            history.setCreateBy(loginUser.getUsername());
            history.setUpdateBy(loginUser.getUsername());

            // 设置数据所有者
            history.setOwnerUserId(loginUser.getUsername());
            if (loginUser.getDeptId() != null) {
                history.setOwnerDeptId(loginUser.getDeptId());
            }
        } else {
            // 如果没有登录用户，使用系统用户ID
            history.setOwnerUserId("system");
        }

        return history;
    }

    @PostMapping(value = "/getHistory.do")
    public ResultBean getHistory(EmployeeProductConfiguration entity) {
        List<SortCondition> sorts = new ArrayList<>();
        SortCondition sortCondition = new SortCondition();
        sortCondition.setDirection(SortCondition.SORT_DESC);
        sortCondition.setProperty(BaseEntity.FIELD_CREATE_TIME);
        sorts.add(sortCondition);

        // 调试日志
        System.out.println("getHistory方法被调用，参数entity: " + (entity != null ? entity.toString() : "null"));
        if (entity != null) {
            System.out.println("ID: " + entity.getId());
            System.out.println("RFID标签号: " + entity.getRfidTagNumber());
        }

        // 查询与当前ID相关的历史记录
        List<EmployeeProductConfigurationHistory> historyRecords = new ArrayList<>();

        try {
            if (entity != null && entity.getId() != null && !entity.getId().trim().isEmpty()) {
                // 使用服务方法查询历史记录
                System.out.println("通过ID查询历史记录: " + entity.getId());
                List<Map<String, Object>> results = employeeProductConfigurationHistoryService.findAllRelatedHistoryByParentId(entity.getId());

                System.out.println("SQL查询结果数量: " + results.size());
                for (Map<String, Object> result : results) {
                    String id = (String) result.get("id");
                    System.out.println("处理历史记录ID: " + id);
                    EmployeeProductConfigurationHistory history = employeeProductConfigurationHistoryService.findById(id);
                    if (history != null) {
                        System.out.println("找到历史记录: 变更类型=" + history.getChangeType() + ", 旧值=" + history.getOldValue() + ", 新值=" + history.getNewValue());

                        // 将历史记录添加到列表中
                        historyRecords.add(history);
                    } else {
                        System.out.println("未找到ID为" + id + "的历史记录");
                    }
                }
            } else {
                System.out.println("当前实体为null或ID为空，无法查询历史记录");
            }
        } catch (Exception e) {
            System.out.println("查询历史记录时发生异常: " + e.getMessage());
            e.printStackTrace();
            return ResultBean.error("查询历史记录时发生异常: " + e.getMessage(), 500);
        }

        // 返回结果
        return ResultBean.success(historyRecords);
    }

    /**
     * 服务记录与查询点击序列号查看服装具体洗涤次数
     *
     * @param entity
     * @return
     */
    @PostMapping(value = "/getxdjl.do")
    public ResultBean getxdjl(EmployeeProductConfiguration entity) {
        List<WmsDeliveryDetail> byContractNumberAndProductSerialNumber = wmsDeliveryDetailService.findByContractNumberAndProductSerialNumber(entity.getContractNumber(), entity.getRfidTagNumberWork(), GlobalContsant.CLOTHING_WASH_TYPE_7);
        return ResultBean.success(byContractNumberAndProductSerialNumber);
    }

    /**
     * 更新服务记录与查询信息导入
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importExcel.do")
    public ResultBean importExcel(@RequestParam("file") MultipartFile file, String contractNumber) {
        try {
            // 创建自定义监听器
            EmployeeProductConfigurationListener listener = new EmployeeProductConfigurationListener(contractNumber);
            Map<String, Object> result = new HashMap<>();
            // 使用自定义监听器读取Excel
            EasyExcel.read(file.getInputStream(), EmployeeProductConfiguration.class, listener)
                    .sheet()
                    .doRead();
            // 检查是否有转换异常
            if (!listener.getErrorMessages().isEmpty()) {
                // 如果有数据转换异常，直接返回错误信息
                result.put("errorList", listener.getErrorMessages());
                result.put("successCount", 0);
                result.put("newCount", 0);
                result.put("updateCount", 0);
                return ResultBean.success(result);
            }
            // 没有转换异常，则处理导入数据
            result = employeeProductConfigurationService.importData(listener.getDataList(), contractNumber);
            return ResultBean.success(result);
        } catch (IOException e) {
            e.printStackTrace();
            return ResultBean.error(e.getMessage(), 500);
        }
    }

    class saveHistoryData extends Thread {
        private List<EmployeeProductConfigurationHistory> allHistoryList;
        private EmployeeProductConfigurationHistoryService employeeProductConfigurationHistoryServiceInfo;

        public saveHistoryData(List<EmployeeProductConfigurationHistory> allHistoryList, EmployeeProductConfigurationHistoryService employeeProductConfigurationHistoryServiceInfo) {
            this.allHistoryList = allHistoryList;
            this.employeeProductConfigurationHistoryServiceInfo = employeeProductConfigurationHistoryServiceInfo;
        }

        @Override
        public void run() {
            employeeProductConfigurationHistoryServiceInfo.saveAll(allHistoryList);
        }
    }

    /**
     * 导出服务记录与查询
     *
     * @param filters
     * @return
     * @throws IOException
     */
    @PostMapping("/export.do")
    public ResultBean export(@RequestList(clazz = FilterCondition.class) List<FilterCondition> filters) throws IOException {

        String fileName = "服务记录与查询";
        fileName = ExportUtil.encodingExcelFilename(fileName);
        String folder = Global.getTempPath() + File.separator + "pio" + File.separator;
        FileUtil.touch(folder + fileName);
        // 模拟数据，实际需要从数据库中获取
        List<EmployeeProductConfigurationExcel> list = new ArrayList<>();

        // 导出的Excel按照序号进行降序排序
        List<SortCondition> sorts = new ArrayList<>();
        if (sorts.isEmpty()) {
            SortCondition sortCondition = new SortCondition();
            sortCondition.setProperty("serialNumber");
            sortCondition.setDirection(SortCondition.SORT_ASC);
            sorts.add(sortCondition);
        }
        List<EmployeeProductConfiguration> allByObject = employeeProductConfigurationService.findAll(filters, sorts);

        for (EmployeeProductConfiguration employeeProductConfiguration : allByObject) {
            EmployeeProductConfigurationExcel employeeProductConfigurationExcel = new EmployeeProductConfigurationExcel();
            BeanUtils.copyProperties(employeeProductConfiguration, employeeProductConfigurationExcel);
            list.add(employeeProductConfigurationExcel);
        }
        EasyExcel.write(folder + fileName, EmployeeProductConfigurationExcel.class)
                .sheet("服务记录与查询")
                .doWrite(list);

        return ResultBean.success(fileName);
    }

    @Autowired
    private RedisCache redisCache;

    @RequestMapping(value = "/dataUpload.do")
    @ResponseBody
    public ResultBean dataUpload(String ids) {
        String string = UUID.randomUUID().toString();
        redisCache.setCacheObject(string, ids, Constants.CAPTCHA_EXPIRATION, TimeUnit.MINUTES);
        return ResultBean.success(string);
    }

    /**
     * 打印服装标签相关接口
     *
     * @param ids
     * @return
     */
    @RequestMapping(value = "/getPrintData", produces = {"application/xml;charset=utf-8"})
    @ResponseBody
    public String getData(String ids) {
        Object cacheObject = redisCache.getCacheObject(ids);
        redisCache.deleteObject(ids);
        ids = cacheObject.toString();
        // 参数校验
        if (ids == null || ids.isEmpty()) {
            return "选择打印的标签！";
        }
        try {
            //获取当前时间
            Date currentTime = new Date();
            //将前端传过来的ids转换成List<String>
            List<String> idList = Arrays.stream(ids.split(","))
                    // 将每个元素转换为字符串
                    .map(String::valueOf)
                    // 将转换后的字符串收集到一个列表中
                    .collect(Collectors.toList());

            //List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
            List<EmployeeProductConfiguration> configList = employeeProductConfigurationService.findByIds(idList);
            if (configList == null || configList.isEmpty()) {
                configList = new ArrayList<>();
            }
            Thread thread = new UpdateStatusThread(configList, employeeProductConfigurationService);
            thread.start();
//            for (EmployeeProductConfiguration config : configList) {
//                //获取配置信息
//                //定义打印次数为0
//                //int printCount = 0;
//                if (config.getPrintCount() == null) {
//                    config.setPrintCount(0);
//                }
//                config.setPrintCount(config.getPrintCount() + 1);
//                config.setPrintTime(currentTime);
//
//                //保存配置信息
//                employeeProductConfigurationService.updateNoUser(config);
//
//                //添加到返回列表
//                //employeeProductConfigurationList.add(config);
//            }

            String xmlString = GenerateXML.generateXMLString(configList);
            return xmlString; // 返回XML字符串
        } catch (Exception e) {
            //异常处理
            e.printStackTrace();
            return "发生错误";
        }
    }

    class UpdateStatusThread extends Thread {
        private List<EmployeeProductConfiguration> configList;
        private EmployeeProductConfigurationService employeeProductConfigurationServiceInfo;

        public UpdateStatusThread(List<EmployeeProductConfiguration> configList, EmployeeProductConfigurationService employeeProductConfigurationService) {
            this.configList = configList;
            this.employeeProductConfigurationServiceInfo = employeeProductConfigurationService;
        }

        @Override
        public void run() {
            for (EmployeeProductConfiguration config : configList) {
                //获取配置信息
                //定义打印次数为0
                //int printCount = 0;
                if (config.getPrintCount() == null) {
                    config.setPrintCount(0);
                }
                config.setPrintCount(config.getPrintCount() + 1);
                config.setPrintTime(new Date());

                //保存配置信息
                employeeProductConfigurationServiceInfo.updateNoUser(config);

                //添加到返回列表
                //employeeProductConfigurationList.add(config);
            }
        }
    }

    /**
     * 根据服务记录主表id查询子表
     *
     * @param entity
     * @return
     */
    @PostMapping("/getDetailByMainId.do")
    public ResultBean getDetailByMainId(EmployeeProductConfiguration entity) {
        try {
            if (entity.getId() == null || entity.getId().trim().isEmpty()) {
                return ResultBean.error("无效的参数：任务单ID不能为空", -1);
            }
            //根据主键id查询子表
            List<EmployeeProductConfigurationDetail> byEmployeeProductConfigurationId = employeeProductConfigurationDetailService.findByEmployeeProductConfigurationId(entity.getId());
            return ResultBean.success(byEmployeeProductConfigurationId);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultBean.error("发生错误: " + e.getMessage(), -1);
        }
    }

    /**
     * 根据子表id删除单条冬夏装详情
     *
     * @param detail
     * @return
     */
    @PostMapping("/deleteByDetailId.do")
    public ResultBean deleteByDetailId(EmployeeProductConfigurationDetail detail) {
        try {
            if (detail == null && detail.getId() == null) {
                return ResultBean.error("请选择要删除的记录", 500);
            }
            //根据子表id删除单条冬夏装详情
            employeeProductConfigurationDetailService.deleteByDetailId(detail.getId());
            return ResultBean.success("删除成功");
        } catch (Exception e) {
            e.printStackTrace();
            return ResultBean.error("删除失败: " + e.getMessage(), 500);
        }
    }


}
